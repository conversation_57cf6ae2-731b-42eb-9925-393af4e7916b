name: Build LauncherDex

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]
  release:
    types: [ created ]

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up JDK 11
      uses: actions/setup-java@v4
      with:
        java-version: '11'
        distribution: 'temurin'
        
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.x'
        
    - name: Set up Android SDK
      uses: android-actions/setup-android@v3
      with:
        api-level: 31
        build-tools: 34.0.0
        
    - name: Cache Gradle packages
      uses: actions/cache@v3
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-
          
    - name: Make build script executable
      run: chmod +x build.sh gradlew
      
    - name: Build with Gradle
      run: ./gradlew createDex
      
    - name: Optimize DEX file
      run: python modify_dex.py
      
    - name: Verify build
      run: |
        ls -la build/libs/
        echo "launcher.dex size: $(stat -c%s build/libs/launcher.dex) bytes"
        
    - name: Upload launcher.dex artifact
      uses: actions/upload-artifact@v3
      with:
        name: launcher-dex
        path: build/libs/launcher.dex
        
    - name: Upload to release (if release)
      if: github.event_name == 'release'
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ github.event.release.upload_url }}
        asset_path: build/libs/launcher.dex
        asset_name: launcher.dex
        asset_content_type: application/octet-stream
