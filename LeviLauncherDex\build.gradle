plugins {
    id 'java'
}

repositories {
    google()
    mavenCentral()
}

dependencies {
    // Android SDK dependencies for compilation
    compileOnly files("${System.getenv('ANDROID_HOME')}/platforms/android-31/android.jar")
}

java {
    sourceCompatibility = JavaVersion.VERSION_1_8
    targetCompatibility = JavaVersion.VERSION_1_8
}

sourceSets {
    main {
        java {
            srcDirs = ['src/main/java']
        }
    }
}

// Task to compile Java sources to class files
task compileJavaClasses(type: JavaCompile) {
    source = sourceSets.main.java
    classpath = configurations.compileClasspath
    destinationDirectory = file("$buildDir/classes")
    options.compilerArgs += ['-Xlint:none']
}

// Task to create JAR file
task createJar(type: Jar, dependsOn: compileJavaClasses) {
    from "$buildDir/classes"
    archiveBaseName = 'LauncherDex'
    archiveVersion = '1.0'
    destinationDirectory = file("$buildDir/libs")
}

// Task to convert JAR to DEX
task createDex(dependsOn: createJar) {
    doLast {
        def jarFile = file("$buildDir/libs/LauncherDex-1.0.jar")
        def dexFile = file("$buildDir/libs/launcher.dex")
        
        // Check if dx tool is available
        def androidHome = System.getenv('ANDROID_HOME')
        if (!androidHome) {
            throw new GradleException("ANDROID_HOME environment variable is not set")
        }
        
        def dxTool = file("$androidHome/build-tools").listFiles()
            ?.findAll { it.isDirectory() }
            ?.sort { it.name }
            ?.reverse()
            ?.find { new File(it, "dx.bat").exists() || new File(it, "dx").exists() }
        
        if (!dxTool) {
            throw new GradleException("dx tool not found in Android SDK build-tools")
        }
        
        def dxCommand = System.getProperty('os.name').toLowerCase().contains('windows') ? 
            "$dxTool/dx.bat" : "$dxTool/dx"
        
        exec {
            commandLine dxCommand, '--dex', "--output=$dexFile", jarFile
        }
        
        println "DEX file created: $dexFile"
    }
}

// Default task
defaultTasks 'createDex'
